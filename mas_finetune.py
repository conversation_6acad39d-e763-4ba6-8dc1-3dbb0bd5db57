import os
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoModel, BitsAndBytesConfig
from peft import get_peft_model, LoraConfig, TaskType, PeftModel, prepare_model_for_kbit_training
import networkx as nx
import matplotlib.pyplot as plt
from tqdm import tqdm
import random
import numpy as np
import wandb
import time
from datetime import datetime


# 设置随机种子以确保可复现性
def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


set_seed(42)


# 数据集类
class TaskDecompositionDataset(Dataset):
    def __init__(self, jsonl_file, tokenizer, max_length=1024):
        self.data = []
        self.tokenizer = tokenizer
        self.max_length = max_length

        # 加载JSONL文件
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    self.data.append(json.loads(line))

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]

        # 提取查询和学习者档案
        query = item['input']['query']
        learner_profile = json.dumps(item['input']['learner_profile'])

        # 提取输出数据
        output = item['output']

        return {
            'query': query,
            'learner_profile': learner_profile,
            'output': output
        }


# 图生成器模型 - 第一阶段
class TaskGraphGenerator(nn.Module):
    def __init__(self, model_name="Qwen/Qwen1.5-1.5B", device="cuda"):
        super(TaskGraphGenerator, self).__init__()
        self.device = device

        # 加载Qwen-1.5B模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)

        # 使用BitsAndBytes进行量化以节省显存
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.float16
        )

        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=bnb_config,
            trust_remote_code=True,
            device_map="auto"
        )

        # 准备模型进行kbit训练
        self.model = prepare_model_for_kbit_training(self.model)

        # 为图生成器应用LoRA - 使用正确的目标模块
        # 针对Qwen模型的特定结构设置target_modules
        peft_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
        )

        try:
            self.model = get_peft_model(self.model, peft_config)
            print("LoRA adapter successfully applied to graph generator")
        except Exception as e:
            print(f"Warning: Failed to apply LoRA adapter to graph generator: {e}")
            print("Continuing without LoRA fine-tuning for graph generator")

    def generate_graph(self, query, learner_profile):
        prompt = f"""
        # Task
        Based on the following query and learner profile, generate a task decomposition graph structure.
        The graph should outline the key components for a structured task plan.

        ## Query
        {query}

        ## Learner Profile
        {learner_profile}

        ## Output Format
        Generate a graph structure with the following components:

        1. AGENTS: Key roles needed for this task
        2. STAGES: Main phases of work
        3. DEPENDENCIES: Critical relationships between stages

        Format your response as:

        AGENTS:
        - [Agent Role 1]: [Brief description of responsibility]
        - [Agent Role 2]: [Brief description of responsibility]
        ...

        STAGES:
        - [Stage 1]: [Brief description]
        - [Stage 2]: [Brief description]
        ...

        DEPENDENCIES:
        - [Stage 1] -> [Stage 2]
        - [Stage 2] -> [Stage 3, Stage 4]
        ...
        """

        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)

        with torch.no_grad():
            outputs = self.model.generate(
                inputs.input_ids,
                max_new_tokens=512,
                temperature=0.7,
                do_sample=True,
                top_p=0.9,
                pad_token_id=self.tokenizer.eos_token_id
            )

        graph_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

        # 提取生成的图结构部分（去除提示）
        try:
            graph_text = graph_text.split("Format your response as:")[1].strip()
        except:
            # 如果分割失败，尝试直接提取AGENTS部分开始的文本
            if "AGENTS:" in graph_text:
                graph_text = graph_text[graph_text.find("AGENTS:"):]

        return graph_text


# 简化的图嵌入模型 - 不使用PyG，改用简单的图表示
class SimpleGraphEmbedder(nn.Module):
    def __init__(self, hidden_dim=256):
        super(SimpleGraphEmbedder, self).__init__()
        self.hidden_dim = hidden_dim

        # 简单的MLP用于节点特征转换
        self.node_transform = nn.Sequential(
            nn.Linear(768, 512),
            nn.ReLU(),
            nn.Linear(512, hidden_dim)
        )

        # 简单的图级别池化
        self.graph_pooling = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.Tanh()
        )

    def text_to_graph_data(self, graph_text, tokenizer, text_encoder):
        """将文本表示的图转换为简单的图数据结构并提取嵌入"""
        G = nx.DiGraph()

        # 解析文本表示
        sections = {}
        current_section = None

        for line in graph_text.split('\n'):
            line = line.strip()
            if not line:
                continue

            if line.endswith(':'):
                current_section = line[:-1]
                sections[current_section] = []
            elif current_section and line.startswith('-'):
                sections[current_section].append(line[1:].strip())

        # 提取节点和边
        nodes = []
        node_mapping = {}
        node_idx = 0

        # 处理AGENTS部分
        if 'AGENTS' in sections:
            for agent_line in sections['AGENTS']:
                if ':' in agent_line:
                    agent, desc = agent_line.split(':', 1)
                    agent = agent.strip()
                    desc = desc.strip()
                    nodes.append((f"agent:{agent}", desc))
                    node_mapping[f"agent:{agent}"] = node_idx
                    node_idx += 1
                    G.add_node(f"agent:{agent}", type='agent', description=desc)

        # 处理STAGES部分
        if 'STAGES' in sections:
            for stage_line in sections['STAGES']:
                if ':' in stage_line:
                    stage, desc = stage_line.split(':', 1)
                    stage = stage.strip()
                    desc = desc.strip()
                    nodes.append((f"stage:{stage}", desc))
                    node_mapping[f"stage:{stage}"] = node_idx
                    node_idx += 1
                    G.add_node(f"stage:{stage}", type='stage', description=desc)

        # 处理DEPENDENCIES部分
        edges = []
        if 'DEPENDENCIES' in sections:
            for dep_line in sections['DEPENDENCIES']:
                if '->' in dep_line:
                    source, targets = dep_line.split('->', 1)
                    source = source.strip()

                    # 处理多个目标的情况
                    for target in targets.split(','):
                        target = target.strip()
                        if source in node_mapping and target in node_mapping:
                            G.add_edge(source, target)
                            edges.append((node_mapping[source], node_mapping[target]))
                        elif f"stage:{source}" in node_mapping and f"stage:{target}" in node_mapping:
                            G.add_edge(f"stage:{source}", f"stage:{target}")
                            edges.append((node_mapping[f"stage:{source}"], node_mapping[f"stage:{target}"]))

        # 如果没有足够的节点或边，创建一个基本图结构
        if len(nodes) < 2:
            nodes = [("default_node:1", "Default node 1"), ("default_node:2", "Default node 2")]
            node_mapping = {"default_node:1": 0, "default_node:2": 1}
            edges = [(0, 1)]
            G.add_node("default_node:1", type='default', description="Default node 1")
            G.add_node("default_node:2", type='default', description="Default node 2")
            G.add_edge("default_node:1", "default_node:2")

        # 提取节点特征
        node_texts = [desc for _, desc in nodes]

        # 使用文本编码器获取节点嵌入
        inputs = tokenizer(node_texts, padding=True, truncation=True, return_tensors="pt").to(text_encoder.device)

        with torch.no_grad():
            outputs = text_encoder(**inputs)
            if hasattr(outputs, 'last_hidden_state'):
                node_embeddings = outputs.last_hidden_state[:, 0, :]  # 使用[CLS]标记的嵌入作为节点表示
            else:
                # 如果模型输出没有last_hidden_state属性，尝试直接使用输出
                node_embeddings = outputs[0][:, 0, :]

        # 创建简单的图数据结构
        graph_data = {
            'node_embeddings': node_embeddings,
            'edges': edges,
            'num_nodes': len(nodes)
        }

        return graph_data, G

    def forward(self, graph_data):
        # 转换节点特征
        node_embeddings = graph_data['node_embeddings']
        transformed_nodes = self.node_transform(node_embeddings)

        # 简单的平均池化作为图嵌入
        graph_embedding = torch.mean(transformed_nodes, dim=0, keepdim=True)
        graph_embedding = self.graph_pooling(graph_embedding)

        return graph_embedding


# 融合模型 - 第二阶段
class TaskDecompositionModel(nn.Module):
    def __init__(self, model_path="models/qwen3-1.5B", graph_dim=256, device="cuda"):
        super(TaskDecompositionModel, self).__init__()
        self.device = device

        # 加载Qwen-1.5B模型和分词器
        self.tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)

        # 使用BitsAndBytes进行量化以节省显存
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.float16
        )

        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            quantization_config=bnb_config,
            trust_remote_code=True,
            device_map="auto"
        )

        # 文本编码器 - 用于编码查询和学习者档案
        self.text_encoder = AutoModel.from_pretrained(
            model_path,
            quantization_config=bnb_config,
            trust_remote_code=True,
            device_map="auto"
        )

        # 图嵌入器 - 使用简化版本
        self.graph_embedder = SimpleGraphEmbedder(hidden_dim=graph_dim).to(device)

        # 融合层 - 将图嵌入和查询嵌入融合
        self.fusion_layer = nn.Linear(768 + graph_dim, 768).to(device)

        # 为任务分解模型应用LoRA - 自动检测目标模块
        target_modules = []
        for name, _ in self.model.named_modules():
            if any(module_type in name for module_type in
                   ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]):
                parent_name = name.rsplit(".", 1)[0]
                if parent_name not in target_modules:
                    target_modules.append(parent_name)

        print(f"Detected target modules for task decomposer: {target_modules}")

        peft_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=target_modules
        )
        self.model = get_peft_model(self.model, peft_config)

    def forward(self, query, learner_profile, graph_text):
        # 1. 将图文本转换为图数据并提取图嵌入
        graph_data, nx_graph = self.graph_embedder.text_to_graph_data(
            graph_text,
            self.tokenizer,
            self.text_encoder
        )
        graph_embedding = self.graph_embedder(graph_data)

        # 2. 编码查询和学习者档案
        query_input = f"Query: {query}\nLearner Profile: {learner_profile}"
        query_inputs = self.tokenizer(query_input, return_tensors="pt").to(self.device)

        with torch.no_grad():
            query_outputs = self.text_encoder(**query_inputs)
            if hasattr(query_outputs, 'last_hidden_state'):
                query_embedding = query_outputs.last_hidden_state[:, 0, :]
            else:
                query_embedding = query_outputs[0][:, 0, :]

        # 3. 融合图嵌入和查询嵌入
        combined_embedding = torch.cat([query_embedding, graph_embedding], dim=1)
        fused_embedding = self.fusion_layer(combined_embedding)

        # 4. 构建生成提示
        graph_summary = self._summarize_graph(nx_graph)

        prompt = f"""
        # Task Decomposition

        ## Query
        {query}

        ## Learner Profile
        {learner_profile}

        ## Task Structure
        {graph_summary}

        ## Output Format
        Based on the information above, generate a detailed task decomposition in JSON format with the following structure:

        ```json
        {{
            "agents": [
                {{
                    "role": "Agent Role",
                    "goal": "Agent's primary objective",
                    "backstory": "Background and expertise of the agent",
                    "tools": ["Tool1", "Tool2"]
                }}
            ],
            "task_plan": [
                {{
                    "stage_id": "S1",
                    "stage_name": "Stage Name",
                    "steps": [
                        {{
                            "step_id": "S1-1",
                            "description": "Detailed step description",
                            "depends_on": [],
                            "agent": "Agent Role"
                        }}
                    ]
                }}
            ],
            "execution_order": ["S1-1", "S1-2", "S2-1"]
        }}
        ```

        ## JSON Output:
        """

        # 5. 生成JSON输出
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)

        # 这里我们不使用融合嵌入直接影响生成，而是通过LoRA适配器学习将图结构信息融入生成过程
        outputs = self.model.generate(
            inputs.input_ids,
            max_new_tokens=2048,
            temperature=0.7,
            do_sample=True,
            top_p=0.9,
            pad_token_id=self.tokenizer.eos_token_id
        )

        generated_text = self.tokenizer.decode(outputs[0][inputs.input_ids.size(1):], skip_special_tokens=True)

        # 提取JSON部分
        try:
            json_str = generated_text.strip()
            if json_str.startswith("```json"):
                json_str = json_str[7:]
            if json_str.endswith("```"):
                json_str = json_str[:-3]

            json_str = json_str.strip()
            result = json.loads(json_str)
            return result
        except json.JSONDecodeError:
            # 如果JSON解析失败，尝试修复或返回部分结果
            return {"error": "JSON解析错误", "text": generated_text}

    def _summarize_graph(self, nx_graph):
        """从NetworkX图生成文本摘要"""
        summary = []

        # 添加节点信息
        agents = []
        stages = []

        for node, data in nx_graph.nodes(data=True):
            if isinstance(node, str) and node.startswith("agent:"):
                agents.append(f"- {node.split(':', 1)[1]}: {data.get('description', '')}")
            elif isinstance(node, str) and node.startswith("stage:"):
                stages.append(f"- {node.split(':', 1)[1]}: {data.get('description', '')}")

        if agents:
            summary.append("AGENTS:")
            summary.extend(agents)
            summary.append("")

        if stages:
            summary.append("STAGES:")
            summary.extend(stages)
            summary.append("")

        # 添加依赖关系
        dependencies = []
        for source, target in nx_graph.edges():
            if isinstance(source, str) and isinstance(target, str):
                if source.startswith("stage:") and target.startswith("stage:"):
                    source_name = source.split(':', 1)[1]
                    target_name = target.split(':', 1)[1]
                    dependencies.append(f"- {source_name} -> {target_name}")

        if dependencies:
            summary.append("DEPENDENCIES:")
            summary.extend(dependencies)

        return "\n".join(summary)


# 完整的两阶段模型
class TwoStageTaskDecompositionModel:
    def __init__(self, model_path="models/qwen3-1.5B", device="cuda"):
        self.device = device
        self.graph_generator = TaskGraphGenerator(model_path, device)
        self.task_decomposer = TaskDecompositionModel(model_path, graph_dim=256, device=device)

    def generate(self, query, learner_profile):
        # 第一阶段：生成任务图
        graph_text = self.graph_generator.generate_graph(query, learner_profile)

        # 第二阶段：根据任务图生成详细JSON
        result = self.task_decomposer(query, learner_profile, graph_text)

        return {
            "graph_structure": graph_text,
            "task_decomposition": result
        }


# 计算结构化相似度的函数
def calculate_structural_similarity(generated, target):
    """计算生成的JSON与目标JSON之间的结构相似度"""
    similarity_score = 0.0

    # 1. 检查agents数量相似度
    if 'agents' in generated and 'agents' in target:
        gen_agents = len(generated['agents'])
        target_agents = len(target['agents'])
        agent_ratio = min(gen_agents, target_agents) / max(gen_agents, target_agents) if max(gen_agents,
                                                                                             target_agents) > 0 else 0
        similarity_score += 0.3 * agent_ratio  # 权重0.3

    # 2. 检查task_plan阶段数量相似度
    if 'task_plan' in generated and 'task_plan' in target:
        gen_stages = len(generated['task_plan'])
        target_stages = len(target['task_plan'])
        stage_ratio = min(gen_stages, target_stages) / max(gen_stages, target_stages) if max(gen_stages,
                                                                                             target_stages) > 0 else 0
        similarity_score += 0.3 * stage_ratio  # 权重0.3

        # 3. 检查步骤数量相似度
        gen_steps = sum(len(stage.get('steps', [])) for stage in generated['task_plan'])
        target_steps = sum(len(stage.get('steps', [])) for stage in target['task_plan'])
        step_ratio = min(gen_steps, target_steps) / max(gen_steps, target_steps) if max(gen_steps,
                                                                                        target_steps) > 0 else 0
        similarity_score += 0.2 * step_ratio  # 权重0.2

    # 4. 检查执行顺序长度相似度
    if 'execution_order' in generated and 'execution_order' in target:
        gen_exec = len(generated['execution_order'])
        target_exec = len(target['execution_order'])
        exec_ratio = min(gen_exec, target_exec) / max(gen_exec, target_exec) if max(gen_exec, target_exec) > 0 else 0
        similarity_score += 0.2 * exec_ratio  # 权重0.2

    return similarity_score


# 训练函数 - 添加wandb集成 (完整版)
def train_model(model, train_dataloader, val_dataloader, epochs=3, lr=5e-5, device="cuda",
                project_name="task-decomposition", run_name=None):
    """
    训练两阶段模型，并使用wandb记录训练过程
    """
    # 初始化wandb
    if run_name is None:
        run_name = f"two-stage-model-{datetime.now().strftime('%Y%m%d-%H%M%S')}"

    wandb.init(project=project_name, name=run_name, config={
        "model_name": "Qwen1.5-1.7B",
        "epochs": epochs,
        "learning_rate": lr,
        "batch_size": train_dataloader.batch_size,
        "graph_dim": 256,
        "lora_r": 16,
        "lora_alpha": 32,
        "lora_dropout": 0.1
    })

    # 优化器
    optimizer = torch.optim.AdamW([
        {'params': model.graph_generator.model.parameters(), 'lr': lr},
        {'params': model.task_decomposer.model.parameters(), 'lr': lr},
        {'params': model.task_decomposer.graph_embedder.parameters(), 'lr': lr},
        {'params': model.task_decomposer.fusion_layer.parameters(), 'lr': lr * 10}
    ])

    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)

    # 创建模型保存目录
    save_dir = f"./models/{run_name}"
    os.makedirs(save_dir, exist_ok=True)

    # 记录最佳验证损失
    best_val_loss = float('inf')

    # 记录训练开始时间
    start_time = time.time()

    for epoch in range(epochs):
        epoch_start_time = time.time()

        # 训练阶段
        model.graph_generator.model.train()
        model.task_decomposer.model.train()
        model.task_decomposer.graph_embedder.train()

        train_loss = 0
        train_struct_sim = 0
        train_steps = 0

        progress_bar = tqdm(train_dataloader, desc=f"Epoch {epoch + 1}/{epochs} [Train]")
        for batch in progress_bar:
            query = batch['query']
            learner_profile = batch['learner_profile']
            target_output = batch['output']

            optimizer.zero_grad()

            # 第一阶段：生成任务图
            graph_texts = []
            for q, p in zip(query, learner_profile):
                graph_text = model.graph_generator.generate_graph(q, p)
                graph_texts.append(graph_text)

                # 记录生成的图结构示例到wandb
                if train_steps == 0 and epoch == 0:
                    wandb.log({"graph_structure_example": wandb.Html(f"<pre>{graph_text}</pre>")})

            # 第二阶段：为每个样本生成JSON输出
            batch_loss = 0
            batch_struct_sim = 0

            for i, (q, p, g, t) in enumerate(zip(query, learner_profile, graph_texts, target_output)):
                # 生成JSON输出
                generated_output = model.task_decomposer(q, p, g)

                # 计算结构相似度
                struct_sim = calculate_structural_similarity(generated_output, t)
                batch_struct_sim += struct_sim

                # 计算损失 - 使用1-结构相似度作为损失
                loss = torch.tensor(1.0 - struct_sim, device=device, requires_grad=True)

                # 记录第一个样本的生成结果到wandb
                if train_steps == 0 and i == 0 and epoch == 0:
                    wandb.log({
                        "generation_example": wandb.Html(
                            f"<h3>Query:</h3><p>{q}</p>"
                            f"<h3>Learner Profile:</h3><p>{p}</p>"
                            f"<h3>Generated Output:</h3><pre>{json.dumps(generated_output, indent=2)}</pre>"
                            f"<h3>Target Output:</h3><pre>{json.dumps(t, indent=2)}</pre>"
                        )
                    })

                batch_loss += loss

            # 平均批次损失和相似度
            batch_loss = batch_loss / len(query)
            batch_struct_sim = batch_struct_sim / len(query)

            batch_loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.graph_generator.model.parameters(), 1.0)
            torch.nn.utils.clip_grad_norm_(model.task_decomposer.model.parameters(), 1.0)

            optimizer.step()

            train_loss += batch_loss.item()
            train_struct_sim += batch_struct_sim
            train_steps += 1

            # 更新进度条
            progress_bar.set_postfix({
                "loss": batch_loss.item(),
                "struct_sim": batch_struct_sim
            })

            # 记录训练批次指标到wandb
            wandb.log({
                "train_batch_loss": batch_loss.item(),
                "train_batch_struct_sim": batch_struct_sim,
                "learning_rate": scheduler.get_last_lr()[0]
            })

        # 更新学习率
        scheduler.step()

        # 计算平均训练损失和相似度
        avg_train_loss = train_loss / train_steps
        avg_train_struct_sim = train_struct_sim / train_steps

        # 验证阶段
        model.graph_generator.model.eval()
        model.task_decomposer.model.eval()
        model.task_decomposer.graph_embedder.eval()

        val_loss = 0
        val_struct_sim = 0
        val_steps = 0

        progress_bar = tqdm(val_dataloader, desc=f"Epoch {epoch + 1}/{epochs} [Val]")
        with torch.no_grad():
            for batch in progress_bar:
                query = batch['query']
                learner_profile = batch['learner_profile']
                target_output = batch['output']

                # 第一阶段：生成任务图
                graph_texts = []
                for q, p in zip(query, learner_profile):
                    graph_text = model.graph_generator.generate_graph(q, p)
                    graph_texts.append(graph_text)

                # 第二阶段：为每个样本生成JSON输出
                batch_loss = 0
                batch_struct_sim = 0

                for i, (q, p, g, t) in enumerate(zip(query, learner_profile, graph_texts, target_output)):
                    # 生成JSON输出
                    generated_output = model.task_decomposer(q, p, g)

                    # 计算结构相似度
                    struct_sim = calculate_structural_similarity(generated_output, t)
                    batch_struct_sim += struct_sim

                    # 计算损失 - 使用1-结构相似度作为损失
                    loss = 1.0 - struct_sim
                    batch_loss += loss

                # 平均批次损失和相似度
                batch_loss = batch_loss / len(query)
                batch_struct_sim = batch_struct_sim / len(query)

                val_loss += batch_loss
                val_struct_sim += batch_struct_sim
                val_steps += 1

                # 更新进度条
                progress_bar.set_postfix({
                    "val_loss": batch_loss,
                    "val_struct_sim": batch_struct_sim
                })

        # 计算平均验证损失和相似度
        avg_val_loss = val_loss / val_steps
        avg_val_struct_sim = val_struct_sim / val_steps

        # 计算本轮epoch的时间
        epoch_time = time.time() - epoch_start_time

        # 打印训练信息
        print(f"Epoch {epoch + 1}/{epochs} - Time: {epoch_time:.2f}s - "
              f"Train Loss: {avg_train_loss:.4f}, Train Struct Sim: {avg_train_struct_sim:.4f}, "
              f"Val Loss: {avg_val_loss:.4f}, Val Struct Sim: {avg_val_struct_sim:.4f}")

        # 记录训练和验证指标到wandb
        wandb.log({
            "epoch": epoch + 1,
            "train_loss": avg_train_loss,
            "train_struct_sim": avg_train_struct_sim,
            "val_loss": avg_val_loss,
            "val_struct_sim": avg_val_struct_sim,
            "epoch_time": epoch_time
        })

        # 保存模型检查点
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss

            # 保存图生成器
            model.graph_generator.model.save_pretrained(f"{save_dir}/graph_generator")
            model.graph_generator.tokenizer.save_pretrained(f"{save_dir}/graph_generator_tokenizer")

            # 保存任务分解器
            model.task_decomposer.model.save_pretrained(f"{save_dir}/task_decomposer")
            model.task_decomposer.tokenizer.save_pretrained(f"{save_dir}/task_decomposer_tokenizer")

            # 保存图嵌入器
            torch.save(model.task_decomposer.graph_embedder.state_dict(), f"{save_dir}/graph_embedder.pt")

            # 保存融合层
            torch.save(model.task_decomposer.fusion_layer.state_dict(), f"{save_dir}/fusion_layer.pt")

            print(f"Saved best model checkpoint to {save_dir} (val_loss: {best_val_loss:.4f})")

            # 记录最佳模型到wandb
            wandb.run.summary["best_val_loss"] = best_val_loss
            wandb.run.summary["best_val_struct_sim"] = avg_val_struct_sim
            wandb.run.summary["best_epoch"] = epoch + 1

    # 计算总训练时间
    total_time = time.time() - start_time
    print(f"Training completed in {total_time:.2f}s")

    # 记录总训练时间到wandb
    wandb.run.summary["total_training_time"] = total_time

    # 关闭wandb
    wandb.finish()

    return model


# 主函数 - 用于训练和评估模型
# 主函数 - 用于训练和评估模型
def main():
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 设置随机种子
    set_seed(42)

    # 模型名称
    model_path = "models/qwen3-1.5B"

    # 命令行参数解析
    import argparse
    parser = argparse.ArgumentParser(description="Train a two-stage task decomposition model")
    parser.add_argument("--train_file", type=str, default="multi_agent_datasets_C1_new.jsonl", help="Path to training data file")
    parser.add_argument("--val_file", type=str, default="multi_agent_datasets_C2_new.jsonl", help="Path to validation data file")
    parser.add_argument("--epochs", type=int, default=3, help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=2, help="Batch size for training")
    parser.add_argument("--lr", type=float, default=5e-5, help="Learning rate")
    parser.add_argument("--wandb_project", type=str, default="task-decomposition", help="W&B project name")
    parser.add_argument("--wandb_run", type=str, default=None, help="W&B run name")
    parser.add_argument("--eval_only", action="store_true", help="Only run evaluation, no training")
    parser.add_argument("--model_path", type=str, default=None, help="Path to load pretrained model for evaluation")
    parser.add_argument("--output_dir", type=str, default="./outputs", help="Directory to save outputs")
    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 初始化tokenizer
    print("Loading tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)

    # 加载数据集
    print(f"Loading datasets from {args.train_file} and {args.val_file}...")
    try:
        train_dataset = TaskDecompositionDataset(args.train_file, tokenizer)
        val_dataset = TaskDecompositionDataset(args.val_file, tokenizer)
        print(f"Loaded {len(train_dataset)} training examples and {len(val_dataset)} validation examples")
    except Exception as e:
        print(f"Error loading datasets: {e}")
        return

    # 创建数据加载器
    train_dataloader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_dataloader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False)

    if args.eval_only and args.model_path:
        # 评估模式 - 加载预训练模型
        print(f"Loading pretrained model from {args.model_path}...")
        try:
            # 初始化模型
            model = TwoStageTaskDecompositionModel(model_path, device)

            # 加载图生成器
            model.graph_generator.model = PeftModel.from_pretrained(
                model.graph_generator.model,
                f"{args.model_path}/graph_generator"
            )

            # 加载任务分解器
            model.task_decomposer.model = PeftModel.from_pretrained(
                model.task_decomposer.model,
                f"{args.model_path}/task_decomposer"
            )

            # 加载图嵌入器
            model.task_decomposer.graph_embedder.load_state_dict(
                torch.load(f"{args.model_path}/graph_embedder.pt")
            )

            # 加载融合层
            model.task_decomposer.fusion_layer.load_state_dict(
                torch.load(f"{args.model_path}/fusion_layer.pt")
            )

            print("Model loaded successfully")
        except Exception as e:
            print(f"Error loading model: {e}")
            return

        # 评估模型
        evaluate_model(model, val_dataloader, device, args.output_dir)
    else:
        # 训练模式
        print("Initializing model...")
        model = TwoStageTaskDecompositionModel(model_path, device)

        # 训练模型
        print("Starting training...")
        train_model(
            model,
            train_dataloader,
            val_dataloader,
            epochs=args.epochs,
            lr=args.lr,
            device=device,
            project_name=args.wandb_project,
            run_name=args.wandb_run
        )

        # 训练后评估
        print("Evaluating model...")
        evaluate_model(model, val_dataloader, device, args.output_dir)


# 评估函数
def evaluate_model(model, val_dataloader, device, output_dir):
    """评估模型并保存结果"""
    model.graph_generator.model.eval()
    model.task_decomposer.model.eval()
    model.task_decomposer.graph_embedder.eval()

    results = []
    total_struct_sim = 0
    total_examples = 0

    print("Running evaluation...")
    with torch.no_grad():
        for batch in tqdm(val_dataloader, desc="Evaluating"):
            query = batch['query']
            learner_profile = batch['learner_profile']
            target_output = batch['output']

            for i, (q, p, t) in enumerate(zip(query, learner_profile, target_output)):
                # 生成结果
                generated = model.generate(q, p)

                # 计算结构相似度
                struct_sim = calculate_structural_similarity(generated["task_decomposition"], t)
                total_struct_sim += struct_sim
                total_examples += 1

                # 保存结果
                results.append({
                    "query": q,
                    "learner_profile": p,
                    "target": t,
                    "generated": generated,
                    "structural_similarity": struct_sim
                })

    # 计算平均结构相似度
    avg_struct_sim = total_struct_sim / total_examples
    print(f"Evaluation complete. Average structural similarity: {avg_struct_sim:.4f}")

    # 保存评估结果
    results_file = os.path.join(output_dir, f"eval_results_{datetime.now().strftime('%Y%m%d-%H%M%S')}.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            "average_structural_similarity": avg_struct_sim,
            "results": results
        }, f, indent=2, ensure_ascii=False)

    print(f"Evaluation results saved to {results_file}")

    return avg_struct_sim, results


if __name__ == "__main__":
    main()



