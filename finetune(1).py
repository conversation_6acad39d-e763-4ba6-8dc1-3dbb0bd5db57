import os
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import get_peft_model, LoraConfig, TaskType, PeftModel
import networkx as nx
import matplotlib.pyplot as plt
from tqdm import tqdm
import random
import numpy as np
# from sklearn.metrics.pairwise import cosine_similarity  # 使用内置方法代替
# from torch_geometric.nn import GCNConv, global_mean_pool
# from torch_geometric.data import Data
from huggingface_hub import login

login(token="*************************************")

# 设置随机种子以确保可复现性
def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


set_seed(42)


# 自定义余弦相似度计算
def cosine_similarity(a, b):
    """计算两个向量的余弦相似度"""
    a = np.array(a)
    b = np.array(b)
    
    dot_product = np.dot(a, b)
    norm_a = np.linalg.norm(a)
    norm_b = np.linalg.norm(b)
    
    if norm_a == 0 or norm_b == 0:
        return 0.0
    
    return dot_product / (norm_a * norm_b)


# 数据集类
class TaskDecompositionDataset(Dataset):
    def __init__(self, jsonl_file, tokenizer, max_length=1024):
        self.data = []
        self.tokenizer = tokenizer
        self.max_length = max_length

        # 加载JSONL文件
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    self.data.append(json.loads(line))

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]

        # 提取查询和学习者档案
        query = item['input']['query']
        learner_profile = json.dumps(item['input']['learner_profile'])

        # 提取输出数据
        output = item['output']

        return {
            'query': query,
            'learner_profile': learner_profile,
            'output': output
        }


# 图生成器模型 - 第一阶段
class TaskGraphGenerator(nn.Module):
    def __init__(self, model_path="Qwen/Qwen2.5-1.5B", device="cuda"):
        super(TaskGraphGenerator, self).__init__()
        self.device = device
        self.model_path = model_path
        
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)

        # 使用BitsAndBytes进行量化以节省显存
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.float16
        )

        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path,
            quantization_config=bnb_config,
            trust_remote_code=True,
            device_map="auto"
        )
        print("-------------------------")
        # 为图生成器应用LoRA
        # peft_config = LoraConfig(
        #     task_type=TaskType.CAUSAL_LM,
        #     inference_mode=False,
        #     r=16,
        #     lora_alpha=32,
        #     lora_dropout=0.1,
        #     target_modules=["c_attn", "c_proj", "w1", "w2"]  # 适配Qwen模型的参数名
        # )
        # self.model = get_peft_model(self.model, peft_config)

    def generate_graph(self, query, learner_profile):
        prompt = f"""
        # Task
        Based on the following query and learner profile, generate a task decomposition graph structure.
        The graph should outline the key components for a structured task plan.

        ## Query
        {query}

        ## Learner Profile
        {learner_profile}

        ## Output Format
        Generate a graph structure with the following components:

        1. AGENTS: Key roles needed for this task
        2. STAGES: Main phases of work
        3. DEPENDENCIES: Critical relationships between stages

        Format your response as:

        AGENTS:
        - [Agent Role 1]: [Brief description of responsibility]
        - [Agent Role 2]: [Brief description of responsibility]
        ...

        STAGES:
        - [Stage 1]: [Brief description]
        - [Stage 2]: [Brief description]
        ...

        DEPENDENCIES:
        - [Stage 1] -> [Stage 2]
        - [Stage 2] -> [Stage 3, Stage 4]
        ...
        """

        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)

        with torch.no_grad():
            outputs = self.model.generate(
                inputs.input_ids,
                max_new_tokens=512,
                temperature=0.7,
                do_sample=True,
                top_p=0.9,
                pad_token_id=self.tokenizer.eos_token_id
            )

        graph_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

        # 提取生成的图结构部分（去除提示）
        try:
            graph_text = graph_text.split("Format your response as:")[1].strip()
        except:
            # 如果分割失败，尝试直接提取AGENTS部分开始的文本
            if "AGENTS:" in graph_text:
                graph_text = graph_text[graph_text.find("AGENTS:"):]

        return graph_text


# 简化的图嵌入模型
# 替换原来的PyTorch Geometric实现
class SimpleGraphEmbedder(nn.Module):
    """简化的图嵌入器，不依赖PyTorch Geometric"""

    def __init__(self, hidden_dim=256):
        super(SimpleGraphEmbedder, self).__init__()
        self.hidden_dim = hidden_dim

        # 使用标准的线性层和注意力机制
        self.node_encoder = nn.Linear(768, hidden_dim)
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8, batch_first=True)
        self.norm = nn.LayerNorm(hidden_dim)
        self.output_proj = nn.Linear(hidden_dim, hidden_dim)

    def forward(self, node_embeddings):
        # node_embeddings: (num_nodes, 768)

        # 编码节点
        x = self.node_encoder(node_embeddings)  # (num_nodes, hidden_dim)

        # 添加位置维度用于注意力
        x = x.unsqueeze(0)  # (1, num_nodes, hidden_dim)

        # 自注意力（模拟图卷积的信息传播）
        attn_out, _ = self.attention(x, x, x)

        # 层归一化
        attn_out = self.norm(attn_out + x)  # 残差连接

        # 全局池化（平均池化）
        graph_embedding = attn_out.mean(dim=1)  # (1, hidden_dim)

        # 输出投影
        graph_embedding = self.output_proj(graph_embedding)

        return graph_embedding

    def text_to_graph_data(self, graph_text, tokenizer, text_encoder):
        """简化的图数据处理"""
        # 解析文本并提取节点描述（与原来相同的逻辑）
        nodes = self._parse_graph_text(graph_text)

        if not nodes:
            # 创建默认节点
            nodes = [("default_agent", "Default planning agent"),
                     ("default_stage", "Default execution stage")]

        # 创建NetworkX图用于依赖关系分析
        nx_graph = self._create_networkx_graph(graph_text, nodes)

        # 提取节点特征
        node_texts = [desc for _, desc in nodes]

        # 使用文本编码器获取节点嵌入
        inputs = tokenizer(node_texts, padding=True, truncation=True, return_tensors="pt").to(text_encoder.device)

        with torch.no_grad():
            outputs = text_encoder(**inputs)
            node_embeddings = outputs.last_hidden_state[:, 0, :]  # 使用[CLS]标记

        return node_embeddings, nx_graph  # 返回节点嵌入和NetworkX图

    def _parse_graph_text(self, graph_text):
        """解析图文本提取节点信息"""
        nodes = []
        sections = {}
        current_section = None

        for line in graph_text.split('\n'):
            line = line.strip()
            if not line:
                continue

            if line.endswith(':'):
                current_section = line[:-1]
                sections[current_section] = []
            elif current_section and line.startswith('-'):
                sections[current_section].append(line[1:].strip())

        # 处理AGENTS部分
        if 'AGENTS' in sections:
            for agent_line in sections['AGENTS']:
                if ':' in agent_line:
                    agent, desc = agent_line.split(':', 1)
                    nodes.append((f"agent_{agent.strip()}", desc.strip()))

        # 处理STAGES部分
        if 'STAGES' in sections:
            for stage_line in sections['STAGES']:
                if ':' in stage_line:
                    stage, desc = stage_line.split(':', 1)
                    nodes.append((f"stage_{stage.strip()}", desc.strip()))

        return nodes

    def _create_networkx_graph(self, graph_text, nodes):
        """从图文本创建NetworkX图"""
        G = nx.DiGraph()
        
        # 添加节点
        for node_id, desc in nodes:
            G.add_node(node_id, description=desc)
        
        # 解析依赖关系
        sections = {}
        current_section = None
        
        for line in graph_text.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            if line.endswith(':'):
                current_section = line[:-1]
                sections[current_section] = []
            elif current_section and line.startswith('-'):
                sections[current_section].append(line[1:].strip())
        
        # 处理DEPENDENCIES部分
        if 'DEPENDENCIES' in sections:
            for dep_line in sections['DEPENDENCIES']:
                if '->' in dep_line:
                    parts = dep_line.split('->')
                    if len(parts) == 2:
                        source = parts[0].strip()
                        targets = [t.strip() for t in parts[1].split(',')]
                        
                        for target in targets:
                            # 添加边（如果节点存在）
                            source_node = f"stage_{source}"
                            target_node = f"stage_{target}"
                            
                            if source_node in [n[0] for n in nodes] and target_node in [n[0] for n in nodes]:
                                G.add_edge(source_node, target_node)
        
        return G


# 融合模型 - 第二阶段
class TaskDecompositionModel(nn.Module):
    def __init__(self, model_path="Qwen/Qwen2.5-1.5B", graph_dim=256, device="cuda"):
        super(TaskDecompositionModel, self).__init__()
        self.device = device

        # 加载Qwen-1.5B模型和分词器
        self.tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)

        # 使用BitsAndBytes进行量化以节省显存
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.float16
        )

        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            quantization_config=bnb_config,
            trust_remote_code=True,
            device_map="auto"
        )

        # 文本编码器 - 用于编码查询和学习者档案
        self.text_encoder = AutoModelForCausalLM.from_pretrained(
            model_path,
            quantization_config=bnb_config,
            trust_remote_code=True,
            device_map="auto"
        )

        # 图嵌入器
        self.graph_embedder = SimpleGraphEmbedder(hidden_dim=graph_dim).to(device)

        # 融合层 - 将图嵌入和查询嵌入融合
        self.fusion_layer = nn.Linear(768 + graph_dim, 768).to(device)

        # 应用LoRA进行高效微调
        # peft_config = LoraConfig(
        #     task_type=TaskType.CAUSAL_LM,
        #     inference_mode=False,
        #     r=16,
        #     lora_alpha=32,
        #     lora_dropout=0.1,
        #     target_modules=["c_attn", "c_proj", "w1", "w2"]  # 适配Qwen模型的参数名
        # )
        # self.model = get_peft_model(self.model, peft_config)

    def forward(self, query, learner_profile, graph_text):
        # 1. 将图文本转换为图数据并提取图嵌入
        graph_data, nx_graph = self.graph_embedder.text_to_graph_data(
            graph_text,
            self.tokenizer,
            self.text_encoder
        )
        graph_data = graph_data.to(self.device)
        graph_embedding = self.graph_embedder(graph_data)

        # 2. 编码查询和学习者档案
        query_input = f"Query: {query}\nLearner Profile: {learner_profile}"
        query_inputs = self.tokenizer(query_input, return_tensors="pt").to(self.device)

        with torch.no_grad():
            query_outputs = self.text_encoder(**query_inputs)
            query_embedding = query_outputs.last_hidden_state[:, 0, :]  # 使用[CLS]标记的嵌入

        # 3. 融合图嵌入和查询嵌入
        combined_embedding = torch.cat([query_embedding, graph_embedding], dim=1)
        fused_embedding = self.fusion_layer(combined_embedding)

        # 4. 构建生成提示
        graph_summary = self._summarize_graph(nx_graph)

        prompt = f"""
        # Task Decomposition

        ## Query
        {query}

        ## Learner Profile
        {learner_profile}

        ## Task Structure
        {graph_summary}

        ## Output Format
        Based on the information above, generate a detailed task decomposition in JSON format with the following structure:

        ```json
        {{
            "agents": [
                {{
                    "role": "Agent Role",
                    "goal": "Agent's primary objective",
                    "backstory": "Background and expertise of the agent",
                    "tools": ["Tool1", "Tool2"]
                }}
            ],
            "task_plan": [
                {{
                    "stage_id": "S1",
                    "stage_name": "Stage Name",
                    "steps": [
                        {{
                            "step_id": "S1-1",
                            "description": "Detailed step description",
                            "depends_on": [],
                            "agent": "Agent Role"
                        }}
                    ]
                }}
            ],
            "execution_order": ["S1-1", "S1-2", "S2-1"]
        }}
        ```

        ## JSON Output:
        """

        # 5. 生成JSON输出
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)

        # 这里我们不使用融合嵌入直接影响生成，而是通过LoRA适配器学习将图结构信息融入生成过程
        outputs = self.model.generate(
            inputs.input_ids,
            max_new_tokens=2048,
            temperature=0.7,
            do_sample=True,
            top_p=0.9,
            pad_token_id=self.tokenizer.eos_token_id
        )

        generated_text = self.tokenizer.decode(outputs[0][inputs.input_ids.size(1):], skip_special_tokens=True)

        # 提取JSON部分
        try:
            json_str = generated_text.strip()
            if json_str.startswith("```json"):
                json_str = json_str[7:]
            if json_str.endswith("```"):
                json_str = json_str[:-3]

            json_str = json_str.strip()
            result = json.loads(json_str)
            return result
        except json.JSONDecodeError:
            # 如果JSON解析失败，尝试修复或返回部分结果
            return {"error": "JSON解析错误", "text": generated_text}

    def _summarize_graph(self, nx_graph):
        """从NetworkX图生成文本摘要"""
        if nx_graph is None:
            return "No graph structure available"
            
        summary = []

        # 添加节点信息
        agents = []
        stages = []

        for node, data in nx_graph.nodes(data=True):
            if node.startswith("agent_"):
                agents.append(f"- {node.split('_', 1)[1]}: {data.get('description', '')}")
            elif node.startswith("stage_"):
                stages.append(f"- {node.split('_', 1)[1]}: {data.get('description', '')}")

        if agents:
            summary.append("AGENTS:")
            summary.extend(agents)
            summary.append("")

        if stages:
            summary.append("STAGES:")
            summary.extend(stages)
            summary.append("")

        # 添加依赖关系
        dependencies = []
        for source, target in nx_graph.edges():
            if source.startswith("stage_") and target.startswith("stage_"):
                source_name = source.split('_', 1)[1]
                target_name = target.split('_', 1)[1]
                dependencies.append(f"- {source_name} -> {target_name}")

        if dependencies:
            summary.append("DEPENDENCIES:")
            summary.extend(dependencies)

        return "\n".join(summary) if summary else "Empty graph structure"


# 完整的两阶段模型
class TwoStageTaskDecompositionModel:
    def __init__(self, model_path="Qwen/Qwen2.5-1.5B", device="cuda"):
        self.device = device
        self.graph_generator = TaskGraphGenerator(model_path, device)
        self.task_decomposer = TaskDecompositionModel(model_path, graph_dim=256, device=device)

    def generate(self, query, learner_profile):
        # 第一阶段：生成任务图
        graph_text = self.graph_generator.generate_graph(query, learner_profile)

        # 第二阶段：根据任务图生成详细JSON
        result = self.task_decomposer(query, learner_profile, graph_text)

        return {
            "graph_structure": graph_text,
            "task_decomposition": result
        }


# 结构化损失计算函数
def calculate_structured_loss(target_output, generated_output, tokenizer, text_encoder, device):
    """
    计算结构化损失，考虑JSON结构的相似性
    """
    try:
        # 如果生成的输出有错误，返回高损失
        if isinstance(generated_output, dict) and "error" in generated_output:
            return torch.tensor(1.0, device=device, requires_grad=True)
        
        # 转换为字符串进行比较
        target_str = json.dumps(target_output, sort_keys=True)
        generated_str = json.dumps(generated_output, sort_keys=True)
        
        # 使用文本编码器计算语义相似度
        texts = [target_str, generated_str]
        inputs = tokenizer(texts, padding=True, truncation=True, max_length=512, return_tensors="pt").to(device)
        
        with torch.no_grad():
            outputs = text_encoder(**inputs)
            embeddings = outputs.last_hidden_state[:, 0, :].cpu().numpy()  # 使用[CLS]标记
        
        # 计算余弦相似度
        similarity = cosine_similarity(embeddings[0], embeddings[1])
        
        # 转换为损失（相似度越高，损失越低）
        loss_value = 1.0 - similarity
        
        # 确保损失值在合理范围内
        loss_value = max(0.0, min(1.0, loss_value))
        
        return torch.tensor(loss_value, device=device, requires_grad=True)
        
    except Exception as e:
        print(f"Loss calculation error: {e}")
        return torch.tensor(1.0, device=device, requires_grad=True)


# 训练函数
def train_model(model, train_dataloader, val_dataloader, epochs=3, lr=5e-5, device="cuda"):
    """
    训练两阶段模型
    """
    # 优化器
    optimizer = torch.optim.AdamW([
        {'params': model.graph_generator.model.parameters(), 'lr': lr},
        {'params': model.task_decomposer.model.parameters(), 'lr': lr},
        {'params': model.task_decomposer.graph_embedder.parameters(), 'lr': lr},
        {'params': model.task_decomposer.fusion_layer.parameters(), 'lr': lr * 10}
    ])

    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)

    for epoch in range(epochs):
        # 训练阶段
        model.graph_generator.model.train()
        model.task_decomposer.model.train()
        model.task_decomposer.graph_embedder.train()

        train_loss = 0
        train_steps = 0

        progress_bar = tqdm(train_dataloader, desc=f"Epoch {epoch + 1}/{epochs} [Train]")
        for batch in progress_bar:
            query = batch['query']
            learner_profile = batch['learner_profile']
            target_output = batch['output']

            optimizer.zero_grad()

            # 第一阶段：生成任务图
            graph_texts = []
            for q, p in zip(query, learner_profile):
                graph_text = model.graph_generator.generate_graph(q, p)
                graph_texts.append(graph_text)

            # 第二阶段：为每个样本生成JSON输出
            batch_loss = 0
            for i, (q, p, g, t) in enumerate(zip(query, learner_profile, graph_texts, target_output)):
                # 生成JSON输出
                generated_output = model.task_decomposer(q, p, g)

                # 使用改进的结构化损失函数
                loss = calculate_structured_loss(
                    t, generated_output, 
                    model.task_decomposer.tokenizer, 
                    model.task_decomposer.text_encoder, 
                    device
                )

                batch_loss += loss

            # 平均批次损失
            batch_loss = batch_loss / len(query)
            batch_loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.graph_generator.model.parameters(), 1.0)
            torch.nn.utils.clip_grad_norm_(model.task_decomposer.model.parameters(), 1.0)

            optimizer.step()

            train_loss += batch_loss.item()
            train_steps += 1

            progress_bar.set_postfix({"loss": batch_loss.item()})

        # 更新学习率
        scheduler.step()

        # 计算平均训练损失
        avg_train_loss = train_loss / train_steps

        # 验证阶段
        model.graph_generator.model.eval()
        model.task_decomposer.model.eval()
        model.task_decomposer.graph_embedder.eval()

        val_loss = 0
        val_steps = 0

        progress_bar = tqdm(val_dataloader, desc=f"Epoch {epoch + 1}/{epochs} [Val]")
        with torch.no_grad():
            for batch in progress_bar:
                query = batch['query']
                learner_profile = batch['learner_profile']
                target_output = batch['output']

                # 第一阶段：生成任务图
                graph_texts = []
                for q, p in zip(query, learner_profile):
                    graph_text = model.graph_generator.generate_graph(q, p)
                    graph_texts.append(graph_text)

                # 第二阶段：为每个样本生成JSON输出
                batch_loss = 0
                for i, (q, p, g, t) in enumerate(zip(query, learner_profile, graph_texts, target_output)):
                    # 生成JSON输出
                    generated_output = model.task_decomposer(q, p, g)

                    # 使用改进的结构化损失函数
                    loss = calculate_structured_loss(
                        t, generated_output, 
                        model.task_decomposer.tokenizer, 
                        model.task_decomposer.text_encoder, 
                        device
                    )

                    batch_loss += loss.item()

                # 平均批次损失
                batch_loss = batch_loss / len(query)

                val_loss += batch_loss
                val_steps += 1

                progress_bar.set_postfix({"val_loss": batch_loss})

        # 计算平均验证损失
        avg_val_loss = val_loss / val_steps

        print(f"Epoch {epoch + 1}/{epochs} - Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}")

        # 保存模型
        save_dir = f"./models/epoch_{epoch + 1}"
        os.makedirs(save_dir, exist_ok=True)

        model.graph_generator.model.save_pretrained(f"{save_dir}/graph_generator")
        model.task_decomposer.model.save_pretrained(f"{save_dir}/task_decomposer")

        # 保存其他组件
        torch.save(model.task_decomposer.graph_embedder.state_dict(), f"{save_dir}/graph_embedder.pt")
        torch.save(model.task_decomposer.fusion_layer.state_dict(), f"{save_dir}/fusion_layer.pt")


# 主函数
def main():
    try:
        # 设置设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {device}")

        # 模型名称
        model_path = "Qwen/Qwen2.5-1.5B"
        print(f"Loading model: {model_path}")

        # 检查数据文件是否存在
        train_file = "multi_agent_datasets_C1_new.jsonl"
        val_file = "multi_agent_datasets_C2_new.jsonl"
        
        if not os.path.exists(train_file):
            print(f"Warning: Training file {train_file} not found!")
            return
        
        if not os.path.exists(val_file):
            print(f"Warning: Validation file {val_file} not found!")
            return

        # 初始化tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        
        # 设置pad_token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        # 加载数据集
        print("Loading datasets...")
        train_dataset = TaskDecompositionDataset(train_file, tokenizer)
        val_dataset = TaskDecompositionDataset(val_file, tokenizer)
        
        print(f"Training samples: {len(train_dataset)}")
        print(f"Validation samples: {len(val_dataset)}")

        # 创建数据加载器（减小batch size以节省内存）
        batch_size = 1 if device.type == 'cuda' else 2
        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

        # 初始化模型
        print("Initializing model...")
        model = TwoStageTaskDecompositionModel(model_path, device)

        # 训练模型
        print("Starting training...")
        train_model(
            model,
            train_dataloader,
            val_dataloader,
            epochs=2,  # 减少epochs以便快速测试
            lr=5e-5,
            device=device
        )

        # 示例推理
        print("\nTesting inference...")
        query = "Perform unified coding standard refactoring across our multi-language microservices architecture"
        learner_profile = json.dumps({
            "level": "advanced",
            "known_languages": ["Java", "Python"],
            "programming_experience": True,
            "education": "graduate"
        })

        result = model.generate(query, learner_profile)
        print("Generated result:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"Error in main function: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
